import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import time
import json
import cv2
from tqdm import tqdm
from sklearn.model_selection import train_test_split
import albumentations as A
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

# Import your existing modules
from darunet import DARU_Net
from dataset import DualSentinelDataset
from data_preprocessing import DataPreprocessor

class EnhancedImageValidator:
    """Advanced image validation for better dataset quality"""
    
    def __init__(self, min_size=(64, 64), max_size=(2048, 2048)):
        self.min_size = min_size
        self.max_size = max_size
        self.preprocessor = DataPreprocessor(use_paper_config=False)
    
    def validate_image_triplet(self, s1_path, s2_path, mask_path):
        """Comprehensive validation of image triplet"""
        try:
            # Load images
            s1_img = cv2.imread(str(s1_path), cv2.IMREAD_GRAYSCALE)
            s2_img = cv2.imread(str(s2_path))
            mask_img = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
            
            if s1_img is None or s2_img is None or mask_img is None:
                return False, "Failed to load images"
            
            # Check dimensions
            if not self._check_dimensions(s1_img, s2_img, mask_img):
                return False, "Dimension mismatch"
            
            # Check image quality
            if not self._check_image_quality(s1_img, s2_img, mask_img):
                return False, "Poor image quality"
            
            # Check mask validity
            if not self._check_mask_validity(mask_img):
                return False, "Invalid mask"
            
            return True, "Valid"
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def _check_dimensions(self, s1_img, s2_img, mask_img):
        """Check if all images have compatible dimensions"""
        h1, w1 = s1_img.shape[:2]
        h2, w2 = s2_img.shape[:2]
        h3, w3 = mask_img.shape[:2]
        
        # Check minimum size
        if h1 < self.min_size[0] or w1 < self.min_size[1]:
            return False
        
        # Check if dimensions are compatible (can be different but should be reasonable)
        return True
    
    def _check_image_quality(self, s1_img, s2_img, mask_img):
        """Check image quality metrics"""
        # Check for all-zero or all-max images
        if np.all(s1_img == 0) or np.all(s1_img == 255):
            return False
        if np.all(s2_img == 0) or np.all(s2_img == 255):
            return False
        
        # Check variance (images should have some variation)
        if np.std(s1_img) < 1.0 or np.std(s2_img) < 1.0:
            return False
        
        return True
    
    def _check_mask_validity(self, mask_img):
        """Check if mask is valid binary mask"""
        unique_values = np.unique(mask_img)
        
        # Should be binary or close to binary
        if len(unique_values) > 10:  # Allow some noise but not too much variation
            return False
        
        # Check if mask has both classes (burned and not burned)
        if len(unique_values) < 2:
            return False
        
        return True

class AdvancedMetricsCalculator:
    """Calculate comprehensive metrics for validation"""
    
    @staticmethod
    def calculate_comprehensive_metrics(outputs, targets):
        """Calculate all relevant metrics"""
        with torch.no_grad():
            # Get predictions
            if outputs.dim() > 3:  # Multi-class output
                predictions = torch.argmax(outputs, dim=1)
            else:  # Binary output
                predictions = (torch.sigmoid(outputs) > 0.5).float()
            
            targets = targets.float()
            predictions = predictions.float()
            
            # Flatten for easier calculation
            pred_flat = predictions.view(-1)
            target_flat = targets.view(-1)
            
            # Basic metrics
            correct = (pred_flat == target_flat).float().sum()
            total = torch.numel(target_flat)
            accuracy = (correct / total) * 100
            
            # Confusion matrix components
            tp = ((pred_flat == 1) & (target_flat == 1)).float().sum()
            fp = ((pred_flat == 1) & (target_flat == 0)).float().sum()
            fn = ((pred_flat == 0) & (target_flat == 1)).float().sum()
            tn = ((pred_flat == 0) & (target_flat == 0)).float().sum()
            
            # Precision, Recall, F1
            precision = tp / (tp + fp + 1e-8)
            recall = tp / (tp + fn + 1e-8)
            f1 = 2 * (precision * recall) / (precision + recall + 1e-8)
            
            # IoU (Intersection over Union)
            intersection = tp
            union = tp + fp + fn
            iou = intersection / (union + 1e-8)
            
            # Dice coefficient
            dice = 2 * intersection / (2 * intersection + fp + fn + 1e-8)
            
            # Specificity
            specificity = tn / (tn + fp + 1e-8)
            
            return {
                'accuracy': accuracy.item(),
                'precision': precision.item(),
                'recall': recall.item(),
                'f1': f1.item(),
                'iou': iou.item(),
                'dice': dice.item(),
                'specificity': specificity.item(),
                'tp': tp.item(),
                'fp': fp.item(),
                'fn': fn.item(),
                'tn': tn.item()
            }

class EnhancedDataAugmentation:
    """Advanced data augmentation specifically for validation improvement"""
    
    def __init__(self, input_size=(256, 256)):
        self.input_size = input_size
        
        # Training augmentation - more aggressive for better generalization
        self.train_transform = A.Compose([
            # Geometric transformations
            A.RandomRotate90(p=0.7),
            A.HorizontalFlip(p=0.5),
            A.VerticalFlip(p=0.5),
            A.ShiftScaleRotate(
                shift_limit=0.1, 
                scale_limit=0.2, 
                rotate_limit=30, 
                p=0.6
            ),
            A.ElasticTransform(
                alpha=1, 
                sigma=50, 
                alpha_affine=50, 
                p=0.3
            ),
            
            # Intensity transformations
            A.RandomBrightnessContrast(
                brightness_limit=0.3, 
                contrast_limit=0.3, 
                p=0.6
            ),
            A.RandomGamma(gamma_limit=(70, 130), p=0.4),
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
            A.GaussianBlur(blur_limit=(3, 7), p=0.2),
            
            # Always resize to target size
            A.Resize(height=input_size[0], width=input_size[1], p=1.0),
        ])
        
        # Validation augmentation - minimal, only resize
        self.val_transform = A.Compose([
            A.Resize(height=input_size[0], width=input_size[1], p=1.0),
        ])
        
        # Test-time augmentation for better validation accuracy
        self.tta_transforms = [
            A.Compose([A.Resize(height=input_size[0], width=input_size[1], p=1.0)]),
            A.Compose([A.HorizontalFlip(p=1.0), A.Resize(height=input_size[0], width=input_size[1], p=1.0)]),
            A.Compose([A.VerticalFlip(p=1.0), A.Resize(height=input_size[0], width=input_size[1], p=1.0)]),
            A.Compose([A.RandomRotate90(p=1.0), A.Resize(height=input_size[0], width=input_size[1], p=1.0)]),
        ]

def create_enhanced_datasets_5_5_split(s1_dir, s2_dir, mask_dir, input_size=(256, 256)):
    """Create datasets with 90% train, 5% val, 5% test split with enhanced validation"""
    print("🔍 Enhanced Dataset Creation with Advanced Image Validation")
    print("=" * 60)
    
    # Initialize validator
    validator = EnhancedImageValidator()
    
    # Get all files
    s1_files = sorted(list(Path(s1_dir).glob('*.png')))
    s2_files = sorted(list(Path(s2_dir).glob('*.png')))
    mask_files = sorted(list(Path(mask_dir).glob('*.png')))
    
    print(f"Found {len(s1_files)} S1 files, {len(s2_files)} S2 files, {len(mask_files)} mask files")
    
    # Create mappings for corresponding files
    s2_mapping = {s2.name.replace('_s2_', '_s1_'): s2 for s2 in s2_files}
    mask_mapping = {mask.name: mask for mask in mask_files}
    
    # Validate and collect valid triplets
    valid_triplets = []
    invalid_count = 0
    
    print("\n🔍 Validating image triplets...")
    for s1_file in tqdm(s1_files, desc="Validating"):
        s2_file = s2_mapping.get(s1_file.name)
        mask_file = mask_mapping.get(s1_file.name)
        
        if s2_file is None or mask_file is None:
            invalid_count += 1
            continue
        
        # Enhanced validation
        is_valid, reason = validator.validate_image_triplet(s1_file, s2_file, mask_file)
        
        if is_valid:
            valid_triplets.append((s1_file, s2_file, mask_file))
        else:
            invalid_count += 1
            if invalid_count <= 5:  # Show first 5 invalid reasons
                print(f"Invalid triplet: {s1_file.name} - {reason}")
    
    print(f"\n✅ Valid triplets: {len(valid_triplets)}")
    print(f"❌ Invalid triplets: {invalid_count}")
    
    if len(valid_triplets) == 0:
        raise ValueError("No valid image triplets found!")
    
    # Split data: 90% train, 5% val, 5% test
    train_triplets, temp_triplets = train_test_split(
        valid_triplets, test_size=0.1, random_state=42, shuffle=True
    )
    
    val_triplets, test_triplets = train_test_split(
        temp_triplets, test_size=0.5, random_state=42, shuffle=True
    )
    
    print(f"\n📊 Enhanced Data Split (90% train, 5% val, 5% test):")
    print(f"   Train: {len(train_triplets)} samples ({len(train_triplets)/len(valid_triplets)*100:.1f}%)")
    print(f"   Val: {len(val_triplets)} samples ({len(val_triplets)/len(valid_triplets)*100:.1f}%)")
    print(f"   Test: {len(test_triplets)} samples ({len(test_triplets)/len(valid_triplets)*100:.1f}%)")
    
    return train_triplets, val_triplets, test_triplets
